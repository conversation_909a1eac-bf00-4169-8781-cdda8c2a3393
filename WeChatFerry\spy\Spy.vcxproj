<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Dev|x64">
      <Configuration>Dev</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{4de80b82-5f6a-4c4c-9d16-1574308110fa}</ProjectGuid>
    <RootNamespace>spy</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <VcpkgTriplet Condition="'$(Platform)'=='x64'">x64-windows-static</VcpkgTriplet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Dev|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Dev|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <TargetName>$(ProjectName)_debug</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Dev|x64'">
    <LinkIncremental>true</LinkIncremental>
    <TargetName>$(ProjectName)_debug</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Label="Vcpkg" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <VcpkgUseStatic>true</VcpkgUseStatic>
    <VcpkgConfiguration>Release</VcpkgConfiguration>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Dev|x64'" Label="Vcpkg">
    <VcpkgUseStatic>true</VcpkgUseStatic>
    <VcpkgConfiguration>Release</VcpkgConfiguration>
  </PropertyGroup>
  <PropertyGroup Label="Vcpkg" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <VcpkgUseStatic>true</VcpkgUseStatic>
  </PropertyGroup>
  <PropertyGroup Label="Vcpkg">
    <VcpkgEnableManifest>true</VcpkgEnableManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;SPY_EXPORTS;_WINDOWS;_USRDLL;ENABLE_DEBUG_LOG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalIncludeDirectories>$(SolutionDir)com;$(SolutionDir)rpc;$(SolutionDir)rpc\nanopb;$(SolutionDir)rpc\proto;$(SolutionDir)smc;$(SolutionDir)spy;$(SolutionDir)vcpkg_installed\x64-windows-static\x64-windows-static\include</AdditionalIncludeDirectories>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <OmitFramePointers>false</OmitFramePointers>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile />
      <DisableSpecificWarnings>4251;4731;4819</DisableSpecificWarnings>
      <AdditionalOptions>/EHa /utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalOptions> /ignore:4099 %(AdditionalOptions)</AdditionalOptions>
      <AdditionalLibraryDirectories>$(SolutionDir)smc;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>iphlpapi.lib;wsock32.lib;ws2_32.lib;crypt32.lib;Codec.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <PreBuildEvent>
      <Command>call "$(SolutionDir)license_check.bat"
if errorlevel 1 exit 1
cd $(SolutionDir)rpc\proto
$(SolutionDir)rpc\tool\protoc --nanopb_out=. wcf.proto</Command>
    </PreBuildEvent>
    <PreBuildEvent>
      <Message>Generating PB files</Message>
    </PreBuildEvent>
    <PostBuildEvent>
      <Command>if not exist $(SolutionDir)Out md $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetFileName) $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetName).exp $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetName).lib $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetName).pdb $(SolutionDir)Out
xcopy /y $(SolutionDir)DISCLAIMER.md $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetFileName) $(SolutionDir)..\clients\python\wcferry
xcopy /y $(SolutionDir)DISCLAIMER.md $(SolutionDir)..\clients\python\wcferry</Command>
    </PostBuildEvent>
    <PostBuildEvent>
      <Message>Copy spy.dll</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Dev|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;SPY_EXPORTS;_WINDOWS;_USRDLL;ENABLE_DEBUG_LOG;ENABLE_WX_LOG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalIncludeDirectories>$(SolutionDir)com;$(SolutionDir)rpc;$(SolutionDir)rpc\nanopb;$(SolutionDir)rpc\proto;$(SolutionDir)smc;$(SolutionDir)spy;$(SolutionDir)vcpkg_installed\x64-windows-static\x64-windows-static\include</AdditionalIncludeDirectories>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <OmitFramePointers>false</OmitFramePointers>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>
      </PrecompiledHeaderOutputFile>
      <DisableSpecificWarnings>4251;4731;4819</DisableSpecificWarnings>
      <AdditionalOptions>/EHa /utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalOptions> /ignore:4099 %(AdditionalOptions)</AdditionalOptions>
      <AdditionalLibraryDirectories>$(SolutionDir)smc;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>iphlpapi.lib;wsock32.lib;ws2_32.lib;crypt32.lib;Codec.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <PreBuildEvent>
      <Command>call "$(SolutionDir)license_check.bat"
if errorlevel 1 exit 1
cd $(SolutionDir)rpc\proto
$(SolutionDir)rpc\tool\protoc --nanopb_out=. wcf.proto</Command>
    </PreBuildEvent>
    <PreBuildEvent>
      <Message>Generating PB files</Message>
    </PreBuildEvent>
    <PostBuildEvent>
      <Command>if not exist $(SolutionDir)Out md $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetFileName) $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetName).exp $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetName).lib $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetName).pdb $(SolutionDir)Out
xcopy /y $(SolutionDir)DISCLAIMER.md $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetFileName) $(SolutionDir)..\clients\python\wcferry
xcopy /y $(SolutionDir)DISCLAIMER.md $(SolutionDir)..\clients\python\wcferry</Command>
    </PostBuildEvent>
    <PostBuildEvent>
      <Message>Copy spy.dll</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;SPY_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalIncludeDirectories>$(SolutionDir)com;$(SolutionDir)rpc;$(SolutionDir)rpc\nanopb;$(SolutionDir)rpc\proto;$(SolutionDir)smc;$(SolutionDir)spy;$(SolutionDir)vcpkg_installed\x64-windows-static\x64-windows-static\include</AdditionalIncludeDirectories>
      <OmitFramePointers>false</OmitFramePointers>
      <PrecompiledHeaderOutputFile />
      <DisableSpecificWarnings>4251;4731;4819</DisableSpecificWarnings>
      <AdditionalOptions>/EHa /utf-8 %(AdditionalOptions)</AdditionalOptions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
      <ModuleDefinitionFile>
      </ModuleDefinitionFile>
      <AdditionalLibraryDirectories>$(SolutionDir)smc;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>iphlpapi.lib;wsock32.lib;ws2_32.lib;crypt32.lib;Codec.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalOptions> /ignore:4099 %(AdditionalOptions)</AdditionalOptions>
    </Link>
    <PreBuildEvent>
      <Message>Generating PB files</Message>
      <Command>call "$(SolutionDir)license_check.bat"
if errorlevel 1 exit 1
cd $(SolutionDir)rpc\proto
$(SolutionDir)rpc\tool\protoc --nanopb_out=. wcf.proto</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message>Copy spy.dll</Message>
      <Command>if not exist $(SolutionDir)Out md $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetFileName) $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetName).exp $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetName).lib $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetName).pdb $(SolutionDir)Out
xcopy /y $(SolutionDir)DISCLAIMER.md $(SolutionDir)Out
xcopy /y $(OutDir)$(TargetFileName) $(SolutionDir)..\clients\python\wcferry
xcopy /y $(SolutionDir)DISCLAIMER.md $(SolutionDir)..\clients\python\wcferry</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\com\log.hpp" />
    <ClInclude Include="..\com\util.h" />
    <ClInclude Include="..\rpc\nanopb\pb.h" />
    <ClInclude Include="..\rpc\nanopb\pb_common.h" />
    <ClInclude Include="..\rpc\nanopb\pb_decode.h" />
    <ClInclude Include="..\rpc\nanopb\pb_encode.h" />
    <ClInclude Include="..\rpc\pb_types.h" />
    <ClInclude Include="..\rpc\pb_util.h" />
    <ClInclude Include="..\rpc\proto\wcf.pb.h" />
    <ClInclude Include="..\smc\codec.h" />
    <ClInclude Include="chatroom_manager.h" />
    <ClInclude Include="misc_manager.h" />
    <ClInclude Include="database_executor.h" />
    <ClInclude Include="..\com\framework.h" />
    <ClInclude Include="contact_manager.h" />
    <ClInclude Include="message_handler.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="rpc_helper.h" />
    <ClInclude Include="rpc_server.h" />
    <ClInclude Include="message_sender.h" />
    <ClInclude Include="spy.h" />
    <ClInclude Include="spy_types.h" />
    <ClInclude Include="sqlite3.h" />
    <ClInclude Include="account_manager.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\com\util.cpp" />
    <ClCompile Include="..\rpc\nanopb\pb_common.c" />
    <ClCompile Include="..\rpc\nanopb\pb_decode.c" />
    <ClCompile Include="..\rpc\nanopb\pb_encode.c" />
    <ClCompile Include="..\rpc\pb_util.cpp" />
    <ClCompile Include="..\rpc\proto\wcf.pb.c" />
    <ClCompile Include="chatroom_manager.cpp" />
    <ClCompile Include="misc_manager.cpp" />
    <ClCompile Include="dllmain.cpp" />
    <ClCompile Include="database_executor.cpp" />
    <ClCompile Include="contact_manager.cpp" />
    <ClCompile Include="message_handler.cpp" />
    <ClCompile Include="rpc_server.cpp" />
    <ClCompile Include="message_sender.cpp" />
    <ClCompile Include="spy.cpp" />
    <ClCompile Include="account_manager.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\rpc\proto\wcf.proto" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="spy.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>